import * as PIXI from 'pixi.js';
import { BaseCompanion } from '../CompanionManager';
import type { CompanionConfig } from '../../types';

/**
 * Farmer companion configuration
 */
export const FARMER_CONFIG: CompanionConfig = {
    type: 'farmer',
    name: 'Farmer',
    spriteSheet: '/assets/farmer/farmer-idle.png',
    frameCount: 24,
    frameSize: { width: 32, height: 64 },
    scale: 4,
    animationSpeed: 0.1,
    followDistance: 80,
    moveSpeed: 3
};

/**
 * Farmer companion class
 */
export class FarmerCompanion extends BaseCompanion {
    constructor(app: PIXI.Application) {
        super(app, FARMER_CONFIG);
    }

    /**
     * Load farmer sprite sheets and extract frames
     */
    protected async loadSprites(): Promise<void> {
        try {
            // Load the farmer idle sprite sheet
            const idleTexture = await PIXI.Assets.load(this.config.spriteSheet);
            
            // Extract frames from the sprite sheet
            // Farmer sprite sheet is 32x64 pixels per frame, 24 frames total
            const frameWidth = this.config.frameSize.width;
            const frameHeight = this.config.frameSize.height;
            const totalFrames = this.config.frameCount;
            
            // Calculate frames per row (assuming horizontal layout)
            const textureWidth = idleTexture.width;
            const framesPerRow = Math.floor(textureWidth / frameWidth);
            
            // Extract idle frames (use all 24 frames for idle animation)
            this.frames.idle = [];
            for (let i = 0; i < totalFrames; i++) {
                const col = i % framesPerRow;
                const row = Math.floor(i / framesPerRow);
                
                const frame = new PIXI.Rectangle(
                    col * frameWidth,
                    row * frameHeight,
                    frameWidth,
                    frameHeight
                );
                
                const texture = new PIXI.Texture(idleTexture, frame);
                this.frames.idle.push(texture);
            }
            
            // For walking animation, we'll use a subset of the idle frames
            // to create a walking effect (every 3rd frame for smoother walking)
            this.frames.walking = [];
            for (let i = 0; i < totalFrames; i += 3) {
                this.frames.walking.push(this.frames.idle[i]);
            }
            
            console.log(`Farmer companion loaded: ${this.frames.idle.length} idle frames, ${this.frames.walking.length} walking frames`);
            
        } catch (error) {
            console.error('Failed to load farmer sprite:', error);
            throw error;
        }
    }

    /**
     * Farmer-specific behavior updates
     */
    update(deltaTime: number, targetPosition: { x: number; y: number }): void {
        // Call base update
        super.update(deltaTime, targetPosition);
        
        // Add farmer-specific behaviors here if needed
        // For example: occasional farming animations, tool interactions, etc.
    }

    /**
     * Farmer-specific animation handling
     */
    protected updateAnimation(): void {
        const speed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y);
        
        // Farmer moves a bit slower than other companions
        if (speed > 0.5) {
            this.setAnimation('walking');
        } else {
            this.setAnimation('idle');
        }
    }
}

/**
 * Factory function to create a farmer companion
 */
export function createFarmerCompanion(app: PIXI.Application): FarmerCompanion {
    return new FarmerCompanion(app);
}

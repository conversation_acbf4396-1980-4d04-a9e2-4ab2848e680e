import * as PIXI from 'pixi.js';
import { BaseCompanion } from '../CompanionManager';
import type { CompanionConfig } from '../../types';

/**
 * Farmer companion configuration
 */
export const FARMER_CONFIG: CompanionConfig = {
    type: 'farmer',
    name: 'Farmer',
    spriteSheet: '/assets/farmer/farmer-idle.png',
    frameCount: 24,
    frameSize: { width: 32, height: 64 },
    scale: 4,
    animationSpeed: 0.08, // Slower animation for more natural idle movement
    followDistance: 80,
    moveSpeed: 3
};

/**
 * Extended frames interface for directional animations
 */
interface FarmerFrames {
    idle: PIXI.Texture[];
    walking: PIXI.Texture[];
    facingRight: PIXI.Texture[];
    facingLeft: PIXI.Texture[];
    facingBack: PIXI.Texture[];
    facingForward: PIXI.Texture[];
}

/**
 * Farmer companion class
 */
export class FarmerCompanion extends BaseCompanion {
    protected declare frames: FarmerFrames;

    constructor(app: PIXI.Application) {
        super(app, FARMER_CONFIG);
        // Initialize extended frames structure
        this.frames = {
            idle: [],
            walking: [],
            facingRight: [],
            facingLeft: [],
            facingBack: [],
            facingForward: []
        };
    }

    /**
     * Load farmer sprite sheets and extract frames
     */
    protected async loadSprites(): Promise<void> {
        try {
            // Load the farmer idle sprite sheet
            const idleTexture = await PIXI.Assets.load(this.config.spriteSheet);

            // Extract frames from the sprite sheet
            // Farmer sprite sheet is 32x64 pixels per frame, 24 frames total
            const frameWidth = this.config.frameSize.width;
            const frameHeight = this.config.frameSize.height;
            const totalFrames = this.config.frameCount;

            // Calculate frames per row (assuming horizontal layout)
            const textureWidth = idleTexture.width;
            const framesPerRow = Math.floor(textureWidth / frameWidth);

            // Extract frames organized by facing direction
            // Frame layout: [0-5] facing right, [6-11] facing back, [12-17] facing left, [18-23] facing forward
            const allFrames: PIXI.Texture[] = [];
            for (let i = 0; i < totalFrames; i++) {
                const col = i % framesPerRow;
                const row = Math.floor(i / framesPerRow);

                const frame = new PIXI.Rectangle(
                    col * frameWidth,
                    row * frameHeight,
                    frameWidth,
                    frameHeight
                );

                const texture = new PIXI.Texture({ source: idleTexture, frame });
                allFrames.push(texture);
            }

            // Organize frames by direction (6 frames each)
            const facingRight = allFrames.slice(0, 6);    // Frames 0-5
            const facingBack = allFrames.slice(6, 12);     // Frames 6-11
            const facingLeft = allFrames.slice(12, 18);    // Frames 12-17
            const facingForward = allFrames.slice(18, 24); // Frames 18-23

            // For idle animation, use facing forward (most natural for idle state)
            this.frames.idle = facingForward;

            // For walking animation, we'll use facing right and left frames
            // We'll switch between these based on movement direction in the animation logic
            this.frames.walking = [...facingRight, ...facingLeft]; // 12 frames total

            // Store directional frames for dynamic switching
            this.frames.facingRight = facingRight;
            this.frames.facingLeft = facingLeft;
            this.frames.facingBack = facingBack;
            this.frames.facingForward = facingForward;

            console.log(`Farmer companion loaded: ${this.frames.idle.length} idle frames (forward), ${this.frames.walking.length} walking frames (right+left), 6 frames per direction`);

        } catch (error) {
            console.error('Failed to load farmer sprite:', error);
            throw error;
        }
    }

    /**
     * Farmer-specific behavior updates
     */
    update(deltaTime: number, targetPosition: { x: number; y: number }): void {
        // Call base update
        super.update(deltaTime, targetPosition);

        // Add farmer-specific behaviors here if needed
        // For example: occasional farming animations, tool interactions, etc.
    }

    /**
     * Override animation method to use directional frames
     */
    setAnimation(state: 'idle' | 'walking' | 'running'): void {
        if (this.currentAnimation === state) return;

        this.currentAnimation = state;

        let frames: PIXI.Texture[];

        if (state === 'idle') {
            // Use forward-facing frames for idle
            frames = this.frames.facingForward;
        } else {
            // For walking/running, determine direction based on velocity
            const isMovingLeft = this.velocity.x < -0.1;
            const isMovingRight = this.velocity.x > 0.1;

            if (isMovingLeft) {
                frames = this.frames.facingLeft;
            } else if (isMovingRight) {
                frames = this.frames.facingRight;
            } else {
                // If not moving horizontally much, use forward-facing walking
                frames = this.frames.facingForward;
            }
        }

        // Create new animated sprite with appropriate frames
        const oldSprite = this.sprite;
        this.sprite = new PIXI.AnimatedSprite(frames);
        this.sprite.anchor.set(0.5, 1.0);

        // Copy properties from old sprite
        const currentScale = oldSprite.scale;
        this.sprite.scale.set(currentScale.x, currentScale.y);

        // Handle position safely for test environment
        if (oldSprite.position) {
            this.sprite.position.set(oldSprite.position.x, oldSprite.position.y);
        }

        this.sprite.zIndex = oldSprite.zIndex;
        this.sprite.animationSpeed = this.config.animationSpeed;
        this.sprite.play();

        // Replace sprite in the stage
        if (oldSprite.parent) {
            const parent = oldSprite.parent;
            parent.removeChild(oldSprite);
            parent.addChild(this.sprite);
        }
    }

    /**
     * Farmer-specific animation handling
     */
    protected updateAnimation(): void {
        const speed = Math.sqrt(this.velocity.x * this.velocity.x + this.velocity.y * this.velocity.y);

        // Farmer moves a bit slower than other companions
        if (speed > 0.5) {
            this.setAnimation('walking');
        } else {
            this.setAnimation('idle');
        }
    }
}

/**
 * Factory function to create a farmer companion
 */
export function createFarmerCompanion(app: PIXI.Application): FarmerCompanion {
    return new FarmerCompanion(app);
}
